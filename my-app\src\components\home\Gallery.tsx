"use client";
import React from "react";
import { LayoutGrid } from "@/components/ui/layout-grid";

export default function Gallery() {
  return (
    <div className="h-screen py-20 w-full">
      <LayoutGrid cards={cards} />
    </div>
  );
}

const SkeletonOne = () => {
  return (
    <div>
      <p className="font-bold md:text-4xl text-xl text-white">
        Commercial HVAC Installation
      </p>
      <p className="font-normal text-base text-white">Downtown Office Complex - 2024</p>
      <p className="font-normal text-base my-4 max-w-lg text-neutral-200">
        Complete HVAC system installation for a 50,000 sq ft office building including energy-efficient units and smart controls.
      </p>
    </div>
  );
};

const SkeletonTwo = () => {
  return (
    <div>
      <p className="font-bold md:text-4xl text-xl text-white">
        Industrial Ductwork System
      </p>
      <p className="font-normal text-base text-white">Manufacturing Plant - 2023</p>
      <p className="font-normal text-base my-4 max-w-lg text-neutral-200">
        Custom ductwork design and installation for manufacturing facility with specialized ventilation requirements.
      </p>
    </div>
  );
};

const SkeletonThree = () => {
  return (
    <div>
      <p className="font-bold md:text-4xl text-xl text-white">
        Smart Thermostat Installation
      </p>
      <p className="font-normal text-base text-white">Residential Home - 2024</p>
      <p className="font-normal text-base my-4 max-w-lg text-neutral-200">
        Smart thermostat and control system installation with mobile app integration and energy monitoring.
      </p>
    </div>
  );
};

const SkeletonFour = () => {
  return (
    <div>
      <p className="font-bold md:text-4xl text-xl text-white">
        Residential HVAC Upgrade
      </p>
      <p className="font-normal text-base text-white">Family Residence - 2024</p>
      <p className="font-normal text-base my-4 max-w-lg text-neutral-200">
        Complete residential HVAC system replacement with high-efficiency equipment and improved air quality.
      </p>
    </div>
  );
};

const SkeletonFive = () => {
  return (
    <div>
      <p className="font-bold md:text-4xl text-xl text-white">
        Preventive Maintenance Service
      </p>
      <p className="font-normal text-base text-white">Various Locations - 2024</p>
      <p className="font-normal text-base my-4 max-w-lg text-neutral-200">
        Comprehensive maintenance service including system inspection, cleaning, and performance optimization.
      </p>
    </div>
  );
};

const SkeletonSix = () => {
  return (
    <div>
      <p className="font-bold md:text-4xl text-xl text-white">
        Energy Efficiency Retrofit
      </p>
      <p className="font-normal text-base text-white">Retail Center - 2023</p>
      <p className="font-normal text-base my-4 max-w-lg text-neutral-200">
        Energy efficiency upgrade project resulting in 30% reduction in energy costs for retail facility.
      </p>
    </div>
  );
};

const cards = [
  {
    id: 1,
    content: <SkeletonOne />,
    className: "md:col-span-2",
    thumbnail: "/Picture2.jpg",
  },
  {
    id: 2,
    content: <SkeletonTwo />,
    className: "col-span-1",
    thumbnail: "/Picture4.jpg",
  },
  {
    id: 3,
    content: <SkeletonThree />,
    className: "col-span-1",
    thumbnail: "/Picture5.jpg",
  },
  {
    id: 4,
    content: <SkeletonFour />,
    className: "md:col-span-2",
    thumbnail: "/Picture6.jpg",
  },
  {
    id: 5,
    content: <SkeletonFive />,
    className: "col-span-1",
    thumbnail: "/Picture8.jpg",
  },
  {
    id: 6,
    content: <SkeletonSix />,
    className: "col-span-1",
    thumbnail: "/Picture11.jpg",
  },
];
