"use client";

import React from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';

export function WhatWeOfferSection() {
  const services = [
    {
      id: 1,
      title: "Complete HVAC System Installation",
      description: "Professional installation of heating, ventilation, and air conditioning systems designed to meet your specific environmental requirements. Our certified technicians ensure optimal performance from day one.",
      image: "/offer1.jpg",
      features: ["Custom system design", "Professional installation", "Quality assurance testing"],
      category: "Installation"
    },
    {
      id: 2,
      title: "Advanced Comfort Cooling Solutions",
      description: "State-of-the-art cooling systems engineered for maximum comfort and energy efficiency. Perfect for offices, hotels, and commercial spaces requiring precise climate control.",
      image: "/offer2.jpg",
      features: ["Energy-efficient operation", "Smart temperature control", "Quiet performance"],
      category: "Cooling"
    },
    {
      id: 3,
      title: "Industrial Mechanical Ventilation",
      description: "Specialized ventilation systems for factories and industrial facilities where proper air circulation is critical for both worker safety and operational efficiency.",
      image: "/offer3.jpg",
      features: ["Industrial-grade systems", "Safety compliance", "High-volume air exchange"],
      category: "Ventilation"
    },
    {
      id: 4,
      title: "Precision Temperature Monitoring",
      description: "Advanced monitoring solutions for sensitive environments requiring exact temperature and humidity control. Essential for data centers, laboratories, and critical facilities.",
      image: "/offer4.jpg",
      features: ["Real-time monitoring", "Alert systems", "Data logging & analytics"],
      category: "Monitoring"
    },
    {
      id: 5,
      title: "HVAC Plant Operations & Maintenance",
      description: "Comprehensive plant operations including maintenance scheduling, performance optimization, and energy efficiency improvements to ensure maximum system longevity.",
      image: "/offer5.jpg",
      features: ["Preventive maintenance", "Performance optimization", "Energy efficiency reports"],
      category: "Maintenance"
    },
    {
      id: 6,
      title: "Specialized Building Flush Out Services",
      description: "Critical air quality services for sensitive environments including hospitals, data centers, laboratories, and clean rooms requiring contamination-free atmospheres.",
      image: "/offer6.jpg",
      features: ["Clean room standards", "Contamination control", "Air quality certification"],
      category: "Air Quality"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8
      }
    }
  };

  return (
    <section className="relative py-24 bg-white overflow-hidden">
      {/* Decorative Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-cyan-500 to-blue-500"></div>
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full opacity-20"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-cyan-100 rounded-full opacity-20"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <h2 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            WHAT <span className="text-blue-600">WE</span> OFFER
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Trust our expertise to deliver fast, reliable results every time. Our skilled technicians ensure your comfort is never compromised with professional solutions tailored to your needs.
          </p>
        </motion.div>

        {/* Services Grid - Alternating Layout */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="space-y-24"
        >
          {services.map((service, index) => (
            <motion.div
              key={service.id}
              variants={itemVariants}
              className={`flex flex-col lg:flex-row items-center gap-12 ${
                index % 2 === 1 ? 'lg:flex-row-reverse' : ''
              }`}
            >
              {/* Image Section */}
              <div className="flex-1 relative group">
                <div className="relative overflow-hidden rounded-3xl shadow-2xl">
                  <Image
                    src={service.image}
                    alt={service.title}
                    width={600}
                    height={400}
                    className="w-full h-80 object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                  <div className="absolute top-6 left-6">
                    <span className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
                      {service.category}
                    </span>
                  </div>
                  <div className="absolute bottom-6 left-6 right-6">
                    <div className="flex flex-wrap gap-2">
                      {service.features.map((feature, idx) => (
                        <span
                          key={idx}
                          className="bg-white/20 backdrop-blur-sm text-white px-3 py-1 rounded-full text-xs"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Content Section */}
              <div className="flex-1 space-y-6">
                <div className="space-y-4">
                  <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 leading-tight">
                    {service.title}
                  </h3>
                  <p className="text-lg text-gray-600 leading-relaxed">
                    {service.description}
                  </p>
                </div>

                {/* Features List */}
                <div className="space-y-3">
                  {service.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                      <span className="text-gray-700 font-medium">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* Action Button */}
                <div className="pt-4">
                  <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                    Learn More
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center mt-24"
        >
          <div className="bg-gradient-to-r from-gray-900 to-blue-900 rounded-3xl p-12 text-white relative overflow-hidden">
            <div className="absolute inset-0 opacity-20">
              <div className="w-full h-full bg-repeat" style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
              }}></div>
            </div>
            <div className="relative z-10">
              <h3 className="text-3xl font-bold mb-4">Ready to Experience Superior HVAC Solutions?</h3>
              <p className="text-gray-300 mb-8 max-w-2xl mx-auto text-lg">
                Get professional consultation and customized solutions for your specific requirements. Our team is ready to deliver excellence.
              </p>
              <button className="bg-white text-gray-900 px-10 py-4 rounded-xl font-bold hover:bg-gray-100 transition-colors duration-300 shadow-lg text-lg">
                Get Free Consultation
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}