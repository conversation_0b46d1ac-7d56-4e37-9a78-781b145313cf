{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/lib/utils.ts"], "sourcesContent": ["import { ClassValue, clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/ui/typewriter-effect-smooth.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\nexport const TypewriterEffectSmooth = ({\r\n  words,\r\n  className,\r\n  cursorClassName,\r\n}: {\r\n  words: {\r\n    text: string;\r\n    className?: string;\r\n  }[];\r\n  className?: string;\r\n  cursorClassName?: string;\r\n}) => {\r\n  const [displayedText, setDisplayedText] = useState(\"\");\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n\r\n  // Combine all words into one continuous text\r\n  const fullText = words.map(word => word.text).join(\" \");\r\n  const firstWordClass = words[0]?.className || \"\";\r\n\r\n  useEffect(() => {\r\n    const timeout = setTimeout(() => {\r\n      if (currentIndex < fullText.length) {\r\n        // Typing the full sentence\r\n        setDisplayedText(fullText.substring(0, currentIndex + 1));\r\n        setCurrentIndex(currentIndex + 1);\r\n      }\r\n      // Once fully typed, keep it displayed (no deleting or cycling)\r\n    }, 100); // Typing speed\r\n\r\n    return () => clearTimeout(timeout);\r\n  }, [currentIndex, fullText]);\r\n\r\n  return (\r\n    <div className={cn(\"flex items-center gap-1\", className)}>\r\n      <div className=\"text-xs sm:text-base md:text-xl lg:text-3xl xl:text-5xl font-bold\">\r\n        <span className={cn(\"text-blue-800 font-medium italic\", firstWordClass)}>\r\n          {displayedText}\r\n        </span>\r\n        <span \r\n          className={cn(\r\n            \"inline-block w-0.5 h-4 sm:h-6 xl:h-12 bg-blue-800 ml-1 animate-pulse\",\r\n            cursorClassName\r\n          )}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKO,MAAM,yBAAyB,CAAC,EACrC,KAAK,EACL,SAAS,EACT,eAAe,EAQhB;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IAEjD,6CAA6C;IAC7C,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,IAAI,CAAC;IACnD,MAAM,iBAAiB,KAAK,CAAC,EAAE,EAAE,aAAa;IAE9C,IAAA,kNAAS,EAAC;QACR,MAAM,UAAU,WAAW;YACzB,IAAI,eAAe,SAAS,MAAM,EAAE;gBAClC,2BAA2B;gBAC3B,iBAAiB,SAAS,SAAS,CAAC,GAAG,eAAe;gBACtD,gBAAgB,eAAe;YACjC;QACA,+DAA+D;QACjE,GAAG,MAAM,eAAe;QAExB,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAc;KAAS;IAE3B,qBACE,8OAAC;QAAI,WAAW,IAAA,yHAAE,EAAC,2BAA2B;kBAC5C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAK,WAAW,IAAA,yHAAE,EAAC,oCAAoC;8BACrD;;;;;;8BAEH,8OAAC;oBACC,WAAW,IAAA,yHAAE,EACX,wEACA;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { TypewriterEffectSmooth } from \"@/components/ui/typewriter-effect-smooth\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\n\r\nconst Hero: React.FC = () => {\r\n  const [isLoaded, setIsLoaded] = useState(false);\r\n  const [currentBg, setCurrentBg] = useState(0);\r\n\r\n  // Typewriter animation words\r\n  const words = [\r\n    { text: \"Breathe\", className: \"text-white font-bold\" },\r\n    { text: \"Easy...\", className: \"text-blue-400 font-bold\" },\r\n    { text: \"Live\", className: \"text-white font-bold\" },\r\n    { text: \"Easy...\", className: \"text-cyan-400 font-bold\" },\r\n  ];\r\n\r\n  // Background rotation\r\n  const backgrounds = [\"/background3.jpg\", \"/background2.jpg\"];\r\n\r\n  useEffect(() => {\r\n    setIsLoaded(true);\r\n    const bgInterval = setInterval(() => {\r\n      setCurrentBg((prev) => (prev + 1) % backgrounds.length);\r\n    }, 8000);\r\n    return () => clearInterval(bgInterval);\r\n  }, [backgrounds.length]);\r\n\r\n  return (\r\n    <section className=\"relative min-h-screen rounded-xl mx-14 flex items-center justify-center overflow-hidden\">\r\n      {/* Background with Parallax Effect */}\r\n      <div className=\"absolute inset-0 bg-fixed\">\r\n        {backgrounds.map((bg, index) => (\r\n          <div\r\n            key={index}\r\n            className={`absolute inset-0 transition-all duration-2000 ${\r\n              index === currentBg\r\n                ? \"opacity-100 scale-100\"\r\n                : \"opacity-0 scale-105\"\r\n            }`}\r\n          >\r\n            <Image\r\n              src={bg}\r\n              alt=\"HVAC Background\"\r\n              fill\r\n              className=\"object-cover\"\r\n              priority={index === 0}\r\n            />\r\n          </div>\r\n        ))}\r\n\r\n        {/* Modern Overlay */}\r\n        <div className=\"absolute inset-0 bg-transparent \" />\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div\r\n        className={`relative z-10 w-full max-w-6xl mx-auto px-6 transition-all duration-1000 ${\r\n          isLoaded ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-8\"\r\n        }`}\r\n      >\r\n        {/* Hero Grid Layout */}\r\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]\">\r\n          {/* Left Column - Content */}\r\n          <div className=\"text-white space-y-8\">\r\n            {/* Main Heading */}\r\n            <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight\">\r\n              <span className=\"text-white\">ST HVAC  SALES & SERVICES</span>\r\n            </h1>\r\n\r\n            {/* Typewriter */}\r\n            <div className=\"text-xl sm:text-2xl\">\r\n              <TypewriterEffectSmooth\r\n                words={words}\r\n                className=\"text-xl sm:text-2xl lg:text-3xl\"\r\n                cursorClassName=\"bg-cyan-400\"\r\n              />\r\n            </div>\r\n\r\n            {/* Description */}\r\n            <p className=\"text-lg text-gray-300 leading-relaxed max-w-lg\">\r\n              Expert heating, cooling, and air quality solutions. From emergency\r\n              repairs to complete system installations, we deliver reliable\r\n              comfort for your home and business.\r\n            </p>\r\n\r\n            {/* Action Buttons */}\r\n            <div className=\"flex flex-col sm:flex-row gap-4\">\r\n              <Link\r\n                href=\"/about\"\r\n                className=\"px-8 py-4 bg-blue-600  text-white rounded-lg font-semibold transition-all duration-300 flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\r\n              >\r\n                Learn More\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Background Indicators */}\r\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2 z-20\">\r\n        {backgrounds.map((_, index) => (\r\n          <button\r\n            key={index}\r\n            onClick={() => setCurrentBg(index)}\r\n            className={`w-2 h-2 rounded-full transition-all duration-300 ${\r\n              index === currentBg\r\n                ? \"bg-blue-600 w-8\"\r\n                : \"bg-white/40 hover:bg-white/60\"\r\n            }`}\r\n          />\r\n        ))}\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Hero;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,OAAiB;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAE3C,6BAA6B;IAC7B,MAAM,QAAQ;QACZ;YAAE,MAAM;YAAW,WAAW;QAAuB;QACrD;YAAE,MAAM;YAAW,WAAW;QAA0B;QACxD;YAAE,MAAM;YAAQ,WAAW;QAAuB;QAClD;YAAE,MAAM;YAAW,WAAW;QAA0B;KACzD;IAED,sBAAsB;IACtB,MAAM,cAAc;QAAC;QAAoB;KAAmB;IAE5D,IAAA,kNAAS,EAAC;QACR,YAAY;QACZ,MAAM,aAAa,YAAY;YAC7B,aAAa,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,YAAY,MAAM;QACxD,GAAG;QACH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,YAAY,MAAM;KAAC;IAEvB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;oBACZ,YAAY,GAAG,CAAC,CAAC,IAAI,sBACpB,8OAAC;4BAEC,WAAW,CAAC,8CAA8C,EACxD,UAAU,YACN,0BACA,uBACJ;sCAEF,cAAA,8OAAC,wIAAK;gCACJ,KAAK;gCACL,KAAI;gCACJ,IAAI;gCACJ,WAAU;gCACV,UAAU,UAAU;;;;;;2BAZjB;;;;;kCAkBT,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBACC,WAAW,CAAC,yEAAyE,EACnF,WAAW,8BAA8B,2BACzC;0BAGF,cAAA,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAAa;;;;;;;;;;;0CAI/B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oLAAsB;oCACrB,OAAO;oCACP,WAAU;oCACV,iBAAgB;;;;;;;;;;;0CAKpB,8OAAC;gCAAE,WAAU;0CAAiD;;;;;;0CAO9D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,uKAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,GAAG,sBACnB,8OAAC;wBAEC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,YACN,oBACA,iCACJ;uBANG;;;;;;;;;;;;;;;;AAYjB;uCAEe", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Image from 'next/image'\n\nconst Client = () => {\n  const clientImages = Array.from({ length: 29 }, (_, i) => `client${i + 1}.jpg`)\n\n  // Duplicate the array to create seamless loop\n  const duplicatedImages = [...clientImages, ...clientImages]\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Our Clients</h2>\n          <p className=\"text-lg text-gray-600\">\n            Trusted by leading companies and organizations\n          </p>\n        </div>\n\n        <div className=\"relative overflow-hidden\">\n          <div className=\"flex animate-scroll\">\n            {duplicatedImages.map((image, index) => (\n              <div\n                key={index}\n                className=\"flex-shrink-0 w-32 h-20 mx-4 relative\"\n              >\n                <Image\n                  src={`/${image}`}\n                  alt={`Client ${(index % clientImages.length) + 1}`}\n                  fill\n                  className=\"object-contain filter grayscale hover:grayscale-0 transition-all duration-300\"\n                  sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                />\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default Client"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAKA,MAAM,SAAS;IACb,MAAM,eAAe,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,IAAM,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;IAE9E,8CAA8C;IAC9C,MAAM,mBAAmB;WAAI;WAAiB;KAAa;IAE3D,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,8OAAC;gCAEC,WAAU;0CAEV,cAAA,8OAAC,wIAAK;oCACJ,KAAK,CAAC,CAAC,EAAE,OAAO;oCAChB,KAAK,CAAC,OAAO,EAAE,AAAC,QAAQ,aAAa,MAAM,GAAI,GAAG;oCAClD,IAAI;oCACJ,WAAU;oCACV,OAAM;;;;;;+BARH;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBrB;uCAEe", "debugId": null}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useMemo, useState } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\n\r\ntype GalleryImage = {\r\n  src: string;\r\n  alt: string;\r\n  category: string;\r\n  description: string;\r\n  location?: string;\r\n  year?: string;\r\n};\r\n\r\n// Professional project gallery\r\nconst projectImages: GalleryImage[] = [\r\n  {\r\n    src: \"/Picture2.jpg\",\r\n    alt: \"Commercial HVAC Installation\",\r\n    category: \"Commercial\",\r\n    description: \"Complete HVAC system installation for a 50,000 sq ft office building including energy-efficient units and smart controls.\",\r\n    location: \"Downtown Office Complex\",\r\n    year: \"2024\"\r\n  },\r\n  {\r\n    src: \"/Picture4.jpg\",\r\n    alt: \"Industrial Ductwork System\",\r\n    category: \"Industrial\",\r\n    description: \"Custom ductwork design and installation for manufacturing facility with specialized ventilation requirements.\",\r\n    location: \"Manufacturing Plant\",\r\n    year: \"2023\"\r\n  },\r\n  {\r\n    src: \"/Picture5.jpg\",\r\n    alt: \"Smart Thermostat Installation\",\r\n    category: \"Residential\",\r\n    description: \"Smart thermostat and control system installation with mobile app integration and energy monitoring.\",\r\n    location: \"Residential Home\",\r\n    year: \"2024\"\r\n  },\r\n  {\r\n    src: \"/Picture6.jpg\",\r\n    alt: \"Residential HVAC Upgrade\",\r\n    category: \"Residential\",\r\n    description: \"Complete residential HVAC system replacement with high-efficiency equipment and improved air quality.\",\r\n    location: \"Family Residence\",\r\n    year: \"2024\"\r\n  },\r\n  {\r\n    src: \"/Picture8.jpg\",\r\n    alt: \"Preventive Maintenance Service\",\r\n    category: \"Maintenance\",\r\n    description: \"Comprehensive maintenance service including system inspection, cleaning, and performance optimization.\",\r\n    location: \"Various Locations\",\r\n    year: \"2024\"\r\n  },\r\n  {\r\n    src: \"/Picture11.jpg\",\r\n    alt: \"Energy Efficiency Retrofit\",\r\n    category: \"Commercial\",\r\n    description: \"Energy efficiency upgrade project resulting in 30% reduction in energy costs for retail facility.\",\r\n    location: \"Retail Center\",\r\n    year: \"2023\"\r\n  },\r\n];\r\n\r\nexport default function Gallery() {\r\n  const images = useMemo(() => projectImages, []);\r\n  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);\r\n  const [filter, setFilter] = useState<string>(\"All\");\r\n\r\n  const categories = [\"All\", ...Array.from(new Set(images.map((img: GalleryImage) => img.category)))];\r\n  const filteredImages = filter === \"All\" ? images : images.filter((img: GalleryImage) => img.category === filter);\r\n\r\n  return (\r\n    <section className=\"py-16 lg:py-24 bg-white\">\r\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n        {/* Professional Header */}\r\n        <motion.header\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <div className=\"flex items-center justify-center space-x-2 mb-6\">\r\n            <div className=\"w-12 h-px bg-blue-600\"></div>\r\n            <span className=\"text-blue-600 font-medium text-sm uppercase tracking-wider\">Project Gallery</span>\r\n            <div className=\"w-12 h-px bg-blue-600\"></div>\r\n          </div>\r\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight\">\r\n            Our Recent HVAC Projects\r\n          </h2>\r\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\r\n            Explore our portfolio of successful HVAC installations, maintenance projects, and energy efficiency upgrades\r\n            across residential, commercial, and industrial properties.\r\n          </p>\r\n        </motion.header>\r\n\r\n        {/* Professional Category Filter */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6, delay: 0.2 }}\r\n          viewport={{ once: true }}\r\n          className=\"flex flex-wrap justify-center gap-4 mb-12\"\r\n        >\r\n          {categories.map((category: string) => (\r\n            <button\r\n              key={category}\r\n              onClick={() => setFilter(category)}\r\n              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${\r\n                filter === category\r\n                  ? \"bg-blue-600 text-white shadow-lg\"\r\n                  : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"\r\n              }`}\r\n            >\r\n              {category}\r\n            </button>\r\n          ))}\r\n        </motion.div>\r\n\r\n        {/* Professional Gallery Grid */}\r\n        <motion.div\r\n          layout\r\n          className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\"\r\n        >\r\n          <AnimatePresence>\r\n            {filteredImages.map((image, index) => (\r\n              <motion.div\r\n                key={image.src}\r\n                layout\r\n                initial={{ opacity: 0, scale: 0.9 }}\r\n                animate={{ opacity: 1, scale: 1 }}\r\n                exit={{ opacity: 0, scale: 0.9 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                className=\"group relative cursor-pointer\"\r\n                onClick={() => setSelectedImage(image)}\r\n              >\r\n                <div className=\"relative overflow-hidden rounded-lg bg-white shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100\">\r\n                  <div className=\"relative aspect-[4/3] overflow-hidden\">\r\n                    <img\r\n                      src={image.src}\r\n                      alt={image.alt}\r\n                      loading=\"lazy\"\r\n                      className=\"h-full w-full object-cover transition duration-300 group-hover:scale-105\"\r\n                    />\r\n\r\n                    {/* Overlay */}\r\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\r\n\r\n                    {/* Category Badge */}\r\n                    <div className=\"absolute top-4 left-4 px-3 py-1 bg-blue-600 text-white text-sm font-medium rounded\">\r\n                      {image.category}\r\n                    </div>\r\n\r\n                    {/* Project Info */}\r\n                    {image.year && (\r\n                      <div className=\"absolute top-4 right-4 px-3 py-1 bg-white/90 text-gray-800 text-sm font-medium rounded\">\r\n                        {image.year}\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* Content */}\r\n                    <div className=\"absolute inset-x-0 bottom-0 p-6 text-white transform translate-y-2 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300\">\r\n                      <h3 className=\"text-lg font-bold mb-1\">{image.alt}</h3>\r\n                      {image.location && (\r\n                        <p className=\"text-sm text-white/80 mb-2\">{image.location}</p>\r\n                      )}\r\n                      <p className=\"text-sm text-white/90 leading-relaxed line-clamp-2\">{image.description}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </AnimatePresence>\r\n        </motion.div>\r\n\r\n        {/* Professional Lightbox Modal */}\r\n        <AnimatePresence>\r\n          {selectedImage && (\r\n            <motion.div\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              exit={{ opacity: 0 }}\r\n              className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4\"\r\n              onClick={() => setSelectedImage(null)}\r\n            >\r\n              <motion.div\r\n                initial={{ scale: 0.9, opacity: 0 }}\r\n                animate={{ scale: 1, opacity: 1 }}\r\n                exit={{ scale: 0.9, opacity: 0 }}\r\n                className=\"relative max-w-4xl max-h-[90vh] bg-white rounded-lg overflow-hidden shadow-2xl\"\r\n                onClick={(e) => e.stopPropagation()}\r\n              >\r\n                <div className=\"relative\">\r\n                  <img\r\n                    src={selectedImage.src}\r\n                    alt={selectedImage.alt}\r\n                    className=\"w-full h-auto max-h-[60vh] object-cover\"\r\n                  />\r\n\r\n                  {/* Close Button */}\r\n                  <button\r\n                    onClick={() => setSelectedImage(null)}\r\n                    className=\"absolute top-4 right-4 w-10 h-10 bg-black/50 rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors duration-300\"\r\n                  >\r\n                    <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                    </svg>\r\n                  </button>\r\n\r\n                  {/* Category Badge */}\r\n                  <div className=\"absolute top-4 left-4 px-3 py-1 bg-blue-600 text-white text-sm font-medium rounded\">\r\n                    {selectedImage.category}\r\n                  </div>\r\n\r\n                  {/* Year Badge */}\r\n                  {selectedImage.year && (\r\n                    <div className=\"absolute top-4 left-20 px-3 py-1 bg-white/90 text-gray-800 text-sm font-medium rounded\">\r\n                      {selectedImage.year}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Professional Content */}\r\n                <div className=\"p-6\">\r\n                  <div className=\"flex items-start justify-between mb-4\">\r\n                    <div>\r\n                      <h3 className=\"text-2xl font-bold text-gray-900 mb-1\">{selectedImage.alt}</h3>\r\n                      {selectedImage.location && (\r\n                        <p className=\"text-blue-600 font-medium\">{selectedImage.location}</p>\r\n                      )}\r\n                    </div>\r\n                    {selectedImage.year && (\r\n                      <span className=\"text-gray-500 text-sm\">{selectedImage.year}</span>\r\n                    )}\r\n                  </div>\r\n                  <p className=\"text-gray-600 leading-relaxed\">{selectedImage.description}</p>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAHA;;;;AAcA,+BAA+B;AAC/B,MAAM,gBAAgC;IACpC;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;QACb,UAAU;QACV,MAAM;IACR;IACA;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;QACb,UAAU;QACV,MAAM;IACR;IACA;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;QACb,UAAU;QACV,MAAM;IACR;IACA;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;QACb,UAAU;QACV,MAAM;IACR;IACA;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;QACb,UAAU;QACV,MAAM;IACR;IACA;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;QACb,UAAU;QACV,MAAM;IACR;CACD;AAEc,SAAS;IACtB,MAAM,SAAS,IAAA,gNAAO,EAAC,IAAM,eAAe,EAAE;IAC9C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAsB;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAS;IAE7C,MAAM,aAAa;QAAC;WAAU,MAAM,IAAI,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC,MAAsB,IAAI,QAAQ;KAAI;IACnG,MAAM,iBAAiB,WAAW,QAAQ,SAAS,OAAO,MAAM,CAAC,CAAC,MAAsB,IAAI,QAAQ,KAAK;IAEzG,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,oMAAM,CAAC,MAAM;oBACZ,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAA6D;;;;;;8CAC7E,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAG,WAAU;sCAA8E;;;;;;sCAG5F,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAOzE,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;4BAEC,SAAS,IAAM,UAAU;4BACzB,WAAW,CAAC,6DAA6D,EACvE,WAAW,WACP,qCACA,+CACJ;sCAED;2BARI;;;;;;;;;;8BAcX,8OAAC,oMAAM,CAAC,GAAG;oBACT,MAAM;oBACN,WAAU;8BAEV,cAAA,8OAAC,4MAAe;kCACb,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC,oMAAM,CAAC,GAAG;gCAET,MAAM;gCACN,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,MAAM;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAC/B,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,WAAU;gCACV,SAAS,IAAM,iBAAiB;0CAEhC,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAK,MAAM,GAAG;gDACd,KAAK,MAAM,GAAG;gDACd,SAAQ;gDACR,WAAU;;;;;;0DAIZ,8OAAC;gDAAI,WAAU;;;;;;0DAGf,8OAAC;gDAAI,WAAU;0DACZ,MAAM,QAAQ;;;;;;4CAIhB,MAAM,IAAI,kBACT,8OAAC;gDAAI,WAAU;0DACZ,MAAM,IAAI;;;;;;0DAKf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA0B,MAAM,GAAG;;;;;;oDAChD,MAAM,QAAQ,kBACb,8OAAC;wDAAE,WAAU;kEAA8B,MAAM,QAAQ;;;;;;kEAE3D,8OAAC;wDAAE,WAAU;kEAAsD,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;+BAvCrF,MAAM,GAAG;;;;;;;;;;;;;;;8BAiDtB,8OAAC,4MAAe;8BACb,+BACC,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,MAAM;4BAAE,SAAS;wBAAE;wBACnB,WAAU;wBACV,SAAS,IAAM,iBAAiB;kCAEhC,cAAA,8OAAC,oMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;gCAAK,SAAS;4BAAE;4BAClC,SAAS;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAChC,MAAM;gCAAE,OAAO;gCAAK,SAAS;4BAAE;4BAC/B,WAAU;4BACV,SAAS,CAAC,IAAM,EAAE,eAAe;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAK,cAAc,GAAG;4CACtB,KAAK,cAAc,GAAG;4CACtB,WAAU;;;;;;sDAIZ,8OAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAKzE,8OAAC;4CAAI,WAAU;sDACZ,cAAc,QAAQ;;;;;;wCAIxB,cAAc,IAAI,kBACjB,8OAAC;4CAAI,WAAU;sDACZ,cAAc,IAAI;;;;;;;;;;;;8CAMzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAyC,cAAc,GAAG;;;;;;wDACvE,cAAc,QAAQ,kBACrB,8OAAC;4DAAE,WAAU;sEAA6B,cAAc,QAAQ;;;;;;;;;;;;gDAGnE,cAAc,IAAI,kBACjB,8OAAC;oDAAK,WAAU;8DAAyB,cAAc,IAAI;;;;;;;;;;;;sDAG/D,8OAAC;4CAAE,WAAU;sDAAiC,cAAc,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzF", "debugId": null}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from 'next/link'\r\nimport Image from 'next/image'\r\nimport React from 'react'\r\nimport { motion } from 'framer-motion'\r\n\r\nconst AboutTeaser = () => {\r\n const keyFeatures = [\r\n    {\r\n      title: \"Licensed Professionals\",\r\n      description: \"Our team consists of fully licensed HVAC technicians with extensive industry experience and ongoing training.\",\r\n      icon: (\r\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n        </svg>\r\n      )\r\n    },\r\n    {\r\n      title: \"24/7 Emergency Service\",\r\n      description: \"Round-the-clock emergency HVAC services to ensure your comfort is never compromised.\",\r\n      icon: (\r\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n        </svg>\r\n      )\r\n    },\r\n    {\r\n      title: \"Energy Efficient Solutions\",\r\n      description: \"Modern HVAC systems designed to reduce energy costs while maintaining optimal indoor comfort.\",\r\n      icon: (\r\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\r\n        </svg>\r\n      )\r\n    }\r\n  ];\r\n\r\n\r\n  return (\r\n    <section className=\"py-16 lg:py-24 bg-white\">\r\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center\">\r\n          {/* Left Content - Professional Images */}\r\n          <motion.div\r\n            initial={{ opacity: 0, x: -30 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"relative\"\r\n          >\r\n            {/* Main Professional Image */}\r\n            <div className=\"relative\">\r\n              <div className=\"relative w-5/6 h-[500px] lg:h-[600px] rounded-lg overflow-hidden shadow-lg\">\r\n                <Image\r\n                  src=\"/about1.jpg\"\r\n                  alt=\"Professional HVAC Technician at Work\"\r\n                  fill\r\n                  className=\"object-cover\"\r\n                  priority\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Secondary Professional Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, scale: 0.9 }}\r\n              whileInView={{ opacity: 1, scale: 1 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              viewport={{ once: true }}\r\n              className=\"absolute top-3/4 right-6 w-4/6 h-[280px] lg:h-[240px] rounded-lg overflow-hidden shadow-lg\"\r\n            >\r\n              <Image\r\n                src=\"/about2.jpg\"\r\n                alt=\"HVAC System Installation\"\r\n                fill\r\n                className=\"object-cover\"\r\n              />\r\n            </motion.div>\r\n          </motion.div>\r\n\r\n          {/* Right Content */}\r\n          <motion.div\r\n            initial={{ opacity: 0, x: 50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"space-y-8\"\r\n          >\r\n            {/* Professional Section Label */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"flex items-center space-x-2\"\r\n            >\r\n              <div className=\"w-12 h-px bg-blue-600\"></div>\r\n              <span className=\"text-blue-600 font-medium text-sm uppercase tracking-wider\">\r\n                About Our Company\r\n              </span>\r\n            </motion.div>\r\n\r\n            {/* Professional Heading */}\r\n            <motion.h2\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 leading-tight\"\r\n            >\r\n              Professional HVAC Services You Can Trust\r\n            </motion.h2>\r\n\r\n            {/* Company Stats */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              viewport={{ once: true }}\r\n              className=\"grid grid-cols-2 gap-6\"\r\n            ></motion.div>\r\n\r\n            {/* Professional Key Features */}\r\n            <div className=\"space-y-4\">\r\n              {keyFeatures.map((feature, index) => (\r\n                <motion.div\r\n                  key={index}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  whileInView={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}\r\n                  viewport={{ once: true }}\r\n                  className=\"flex items-start gap-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-300\"\r\n                >\r\n                  <div className=\"flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\">\r\n                    {feature.icon}\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">\r\n                      {feature.title}\r\n                    </h4>\r\n                    <p className=\"text-gray-600 text-sm leading-relaxed\">\r\n                      {feature.description}\r\n                    </p>\r\n                  </div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Professional CTA Button */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"pt-4\"\r\n            >\r\n              <Link\r\n                href=\"/about\"\r\n                className=\"inline-flex items-center gap-2 px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-300\"\r\n              >\r\n                Learn More About Us\r\n                <svg\r\n                  className=\"w-4 h-4\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth={2}\r\n                    d=\"M17 8l4 4m0 0l-4 4m4-4H3\"\r\n                  />\r\n                </svg>\r\n              </Link>\r\n            </motion.div>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\nexport default AboutTeaser\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,cAAc;IACnB,MAAM,cAAc;QACjB;YACE,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;KACD;IAGD,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wIAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,WAAU;wCACV,QAAQ;;;;;;;;;;;;;;;;0CAMd,8OAAC,oMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAEV,cAAA,8OAAC,wIAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,IAAI;oCACJ,WAAU;;;;;;;;;;;;;;;;;kCAMhB,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAGV,8OAAC,oMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAA6D;;;;;;;;;;;;0CAM/E,8OAAC,oMAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;0CAKD,8OAAC,oMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;;;;;0CAIZ,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,SAAS,sBACzB,8OAAC,oMAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM,QAAQ;wCAAI;wCACtD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,8OAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;;;;;;;;uCAfnB;;;;;;;;;;0CAuBX,8OAAC,oMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAEV,cAAA,8OAAC,uKAAI;oCACH,MAAK;oCACL,WAAU;;wCACX;sDAEC,8OAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtB;uCAEe", "debugId": null}}, {"offset": {"line": 1276, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport Image from 'next/image';\r\n\r\nexport function WhyChooseUsSection() {\r\n\r\n  return (\r\n    <section className=\"py-16 lg:py-24 bg-blue-50\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-14 lg:gap-20 items-center\">\r\n          <motion.div\r\n            initial={{ opacity: 0, x: -50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"space-y-8\"\r\n          >\r\n            {/* Header */}\r\n            <div className=\"mb-12\">\r\n              <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">\r\n                Why Choose Us\r\n              </h1>\r\n              <p className=\"text-gray-600 text-lg\">\r\n                Our commitment to excellence and customer satisfaction\r\n              </p>\r\n            </div>\r\n\r\n            {/* Numbered Points Grid */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\r\n              {/* Point 01 */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"space-y-4\"\r\n              >\r\n                <div className=\"text-6xl font-black text-blue-400\">01</div>\r\n                <h3 className=\"text-xl font-bold text-gray-900\">\r\n                  Fast Breakdown Recovery\r\n                </h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  <span className=\"font-semibold text-gray-900\">W</span>e understand the importance of breakdown recovery lead time to our customers.\r\n                </p>\r\n              </motion.div>\r\n\r\n              {/* Point 02 */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                viewport={{ once: true }}\r\n                className=\"space-y-4\"\r\n              >\r\n                <div className=\"text-6xl font-black text-blue-400\">02</div>\r\n                <h3 className=\"text-xl font-bold text-gray-900\">\r\n                  Reliable System & Manpower\r\n                </h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  <span className=\"font-semibold text-gray-900\">O</span>ur unique internal System, together with our dedicated manpower ensure that we deliver on time every time – We Won't Be Beaten.\r\n                </p>\r\n              </motion.div>\r\n\r\n              {/* Point 03 */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.3 }}\r\n                viewport={{ once: true }}\r\n                className=\"space-y-4\"\r\n              >\r\n                <div className=\"text-6xl font-black text-blue-400\">03</div>\r\n                <h3 className=\"text-xl font-bold text-gray-900\">\r\n                  ST HVAC Maintenance Method\r\n                </h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  <span className=\"font-semibold text-gray-900\">O</span>ur unique 'ST HVAC Maintenance method is the quickest and safest process around, drastically reducing breakdown time and Maintenance costs.\r\n                </p>\r\n              </motion.div>\r\n\r\n              {/* Point 04 */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.4 }}\r\n                viewport={{ once: true }}\r\n                className=\"space-y-4\"\r\n              >\r\n                <div className=\"text-6xl font-black text-blue-400\">04</div>\r\n                <h3 className=\"text-xl font-bold text-gray-900\">\r\n                  Competitive Rates\r\n                </h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  <span className=\"font-semibold text-gray-900\">O</span>ur Rates are very Competitive with no compromise attitude as far as quality is concerned.\r\n                </p>\r\n              </motion.div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Right Column - Large Image */}\r\n          <motion.div\r\n            initial={{ opacity: 0, x: 50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.5 }}\r\n            viewport={{ once: true }}\r\n            className=\"relative\"\r\n          >\r\n            <div className=\"relative  w-5/6 h-[500px] lg:h-[600px] rounded-2xl overflow-hidden shadow-2xl\">\r\n              <Image\r\n                src=\"/background.jpg\"\r\n                alt=\"Professional HVAC Services\"\r\n                fill\r\n                className=\"object-cover\"\r\n              />             \r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n\r\n\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAMO,SAAS;IAEd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAMvC,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,oMAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAGhD,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAK,WAAU;kEAA8B;;;;;;oDAAQ;;;;;;;;;;;;;kDAK1D,8OAAC,oMAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAGhD,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAK,WAAU;kEAA8B;;;;;;oDAAQ;;;;;;;;;;;;;kDAK1D,8OAAC,oMAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAGhD,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAK,WAAU;kEAA8B;;;;;;oDAAQ;;;;;;;;;;;;;kDAK1D,8OAAC,oMAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAGhD,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAK,WAAU;kEAA8B;;;;;;oDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9D,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,wIAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,IAAI;gCACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B", "debugId": null}}, {"offset": {"line": 1644, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\n\nexport function SpecialtySection() {\n  const specialtyServices = [\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n        </svg>\n      ),\n      title: \"Commercial HVAC\",\n      description: \"Complete commercial heating and cooling solutions\"\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n        </svg>\n      ),\n      title: \"Residential HVAC\",\n      description: \"Home comfort solutions and energy efficiency\"\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n      ),\n      title: \"Maintenance Plans\",\n      description: \"Preventive maintenance and service contracts\"\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n        </svg>\n      ),\n      title: \"Energy Audits\",\n      description: \"Efficiency assessments and optimization\"\n    }\n  ];\n\n  return (\n    <section className=\"py-16 lg:py-24 bg-white\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl\">\n        {/* Specialty Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"bg-gray-50 rounded-2xl p-8 lg:p-12 shadow-lg\"\n        >\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              <span className=\"text-blue-600\">SPECIALTY</span> WITHIN\n            </h2>\n            <div className=\"w-24 h-1 bg-blue-600 mx-auto\"></div>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            {/* Left - Main Text */}\n            <div className=\"space-y-6\">\n              <div className=\"bg-blue-50 rounded-xl p-8 border border-blue-100\">\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4 leading-relaxed\">\n                  <span className=\"text-blue-600\">O</span>ur all locations are staffed by trained and well-equipped personnel. Rapid response and speedy results can be counted on. Easy access is further enhanced through Service Solutions Providers, minimizing down-time and ensuring quick satisfaction.\n                </h3>\n                <div className=\"grid grid-cols-2 gap-4 mt-6\">\n                  <div className=\"text-center p-4 bg-white rounded-lg shadow-sm\">\n                    <div className=\"text-2xl font-bold text-blue-600 mb-1\">24/7</div>\n                    <div className=\"text-sm text-gray-600\">Service Available</div>\n                  </div>\n                  <div className=\"text-center p-4 bg-white rounded-lg shadow-sm\">\n                    <div className=\"text-2xl font-bold text-blue-600 mb-1\">100%</div>\n                    <div className=\"text-sm text-gray-600\">Satisfaction Rate</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Right - Services Grid */}\n            <div className=\"grid grid-cols-2 gap-4\">\n              {specialtyServices.map((service, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: 0.1 + index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"bg-white rounded-lg p-4 text-center hover:bg-blue-50 transition-colors duration-300 shadow-md\"\n                >\n                  <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3\">\n                    {service.icon}\n                  </div>\n                  <h4 className=\"font-semibold text-gray-900 text-sm mb-2\">\n                    {service.title}\n                  </h4>\n                  <p className=\"text-gray-600 text-xs\">\n                    {service.description}\n                  </p>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAMO,SAAS;IACd,MAAM,oBAAoB;QACxB;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;;kCAC/E,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;kCACrE,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBAEb,cAAA,8OAAC,oMAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAgB;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAQ;;;;;;;sDAE1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;sEACvD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;sEACvD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO/C,8OAAC;gCAAI,WAAU;0CACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,8OAAC,oMAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM,QAAQ;wCAAI;wCACtD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI;;;;;;0DAEf,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;;uCAdjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBvB", "debugId": null}}, {"offset": {"line": 1997, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\n\r\nexport function WhatWeOfferSection() {\r\n  const services = [\r\n    {\r\n      id: 1,\r\n      title: \"Complete HVAC System Installation\",\r\n      description: \"Professional installation of heating, ventilation, and air conditioning systems designed to meet your specific environmental requirements. Our certified technicians ensure optimal performance from day one.\",\r\n      icon: (\r\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\r\n        </svg>\r\n      ),\r\n      features: [\"Custom system design\", \"Professional installation\", \"Quality assurance testing\"],\r\n      gradient: \"from-blue-500 to-cyan-500\"\r\n    },\r\n    {\r\n      id: 2,\r\n      title: \"Advanced Comfort Cooling Solutions\",\r\n      description: \"State-of-the-art cooling systems engineered for maximum comfort and energy efficiency. Perfect for offices, hotels, and commercial spaces requiring precise climate control.\",\r\n      icon: (\r\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 3H5a2 2 0 00-2 2v12a4 4 0 004 4h2a2 2 0 002-2V5a2 2 0 00-2-2z\" />\r\n        </svg>\r\n      ),\r\n      features: [\"Energy-efficient operation\", \"Smart temperature control\", \"Quiet performance\"],\r\n      gradient: \"from-cyan-500 to-blue-500\"\r\n    },\r\n    {\r\n      id: 3,\r\n      title: \"Industrial Mechanical Ventilation\",\r\n      description: \"Specialized ventilation systems for factories and industrial facilities where proper air circulation is critical for both worker safety and operational efficiency.\",\r\n      icon: (\r\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\r\n        </svg>\r\n      ),\r\n      features: [\"Industrial-grade systems\", \"Safety compliance\", \"High-volume air exchange\"],\r\n      gradient: \"from-green-500 to-teal-500\"\r\n    },\r\n    {\r\n      id: 4,\r\n      title: \"Precision Temperature Monitoring\",\r\n      description: \"Advanced monitoring solutions for sensitive environments requiring exact temperature and humidity control. Essential for data centers, laboratories, and critical facilities.\",\r\n      icon: (\r\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\r\n        </svg>\r\n      ),\r\n      features: [\"Real-time monitoring\", \"Alert systems\", \"Data logging & analytics\"],\r\n      gradient: \"from-purple-500 to-pink-500\"\r\n    },\r\n    {\r\n      id: 5,\r\n      title: \"HVAC Plant Operations & Maintenance\",\r\n      description: \"Comprehensive plant operations including maintenance scheduling, performance optimization, and energy efficiency improvements to ensure maximum system longevity.\",\r\n      icon: (\r\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n        </svg>\r\n      ),\r\n      features: [\"Preventive maintenance\", \"Performance optimization\", \"Energy efficiency reports\"],\r\n      gradient: \"from-orange-500 to-red-500\"\r\n    },\r\n    {\r\n      id: 6,\r\n      title: \"Specialized Building Flush Out Services\",\r\n      description: \"Critical air quality services for sensitive environments including hospitals, data centers, laboratories, and clean rooms requiring contamination-free atmospheres.\",\r\n      icon: (\r\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\r\n        </svg>\r\n      ),\r\n      features: [\"Clean room standards\", \"Contamination control\", \"Air quality certification\"],\r\n      gradient: \"from-teal-500 to-green-500\"\r\n    }\r\n  ];\r\n\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1\r\n      }\r\n    }\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 30 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        duration: 0.6\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <section className=\"relative py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50 overflow-hidden\">\r\n      {/* Background Elements */}\r\n      <div className=\"absolute inset-0 opacity-30\">\r\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse\"></div>\r\n        <div className=\"absolute bottom-20 right-10 w-72 h-72 bg-cyan-200 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-1000\"></div>\r\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-purple-100 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-500\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        {/* Header */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\r\n            <span className=\"text-blue-600\">COMPREHENSIVE</span> HVAC SOLUTIONS\r\n          </h2>\r\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\r\n            Trust our expertise to deliver fast, reliable results every time. Our skilled technicians ensure your comfort is never compromised with professional solutions tailored to your needs.\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Services Grid */}\r\n        <motion.div\r\n          variants={containerVariants}\r\n          initial=\"hidden\"\r\n          whileInView=\"visible\"\r\n          viewport={{ once: true }}\r\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\"\r\n        >\r\n          {services.map((service) => (\r\n            <motion.div\r\n              key={service.id}\r\n              variants={itemVariants}\r\n              className=\"group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-blue-200 overflow-hidden\"\r\n            >\r\n              {/* Gradient Background on Hover */}\r\n              <div className={`absolute inset-0 bg-gradient-to-br ${service.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}></div>\r\n\r\n              {/* Icon */}\r\n              <div className={`relative z-10 w-16 h-16 bg-gradient-to-br ${service.gradient} rounded-xl flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-transform duration-300`}>\r\n                {service.icon}\r\n              </div>\r\n\r\n              {/* Content */}\r\n              <div className=\"relative z-10\">\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300\">\r\n                  {service.title}\r\n                </h3>\r\n                <p className=\"text-gray-600 mb-6 leading-relaxed\">\r\n                  {service.description}\r\n                </p>\r\n\r\n                {/* Features */}\r\n                <ul className=\"space-y-2\">\r\n                  {service.features.map((feature, index) => (\r\n                    <li key={index} className=\"flex items-center text-sm text-gray-500\">\r\n                      <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full mr-3\"></div>\r\n                      {feature}\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              </div>\r\n\r\n              {/* Hover Effect Border */}\r\n              <div className=\"absolute inset-0 rounded-2xl border-2 border-transparent group-hover:border-blue-200 transition-colors duration-300\"></div>\r\n            </motion.div>\r\n          ))}\r\n        </motion.div>\r\n\r\n        {/* Bottom CTA */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8, delay: 0.3 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mt-16\"\r\n        >\r\n          <div className=\"bg-gradient-to-r from-blue-600 to-cyan-600 rounded-2xl p-8 text-white\">\r\n            <h3 className=\"text-2xl font-bold mb-4\">Ready to Experience Superior HVAC Solutions?</h3>\r\n            <p className=\"text-blue-100 mb-6 max-w-2xl mx-auto\">\r\n              Get professional consultation and customized solutions for your specific requirements. Our team is ready to deliver excellence.\r\n            </p>\r\n            <button className=\"bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300 shadow-lg\">\r\n              Get Free Consultation\r\n            </button>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAKO,SAAS;IACd,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,UAAU;gBAAC;gBAAwB;gBAA6B;aAA4B;YAC5F,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,UAAU;gBAAC;gBAA8B;gBAA6B;aAAoB;YAC1F,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,UAAU;gBAAC;gBAA4B;gBAAqB;aAA2B;YACvF,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,UAAU;gBAAC;gBAAwB;gBAAiB;aAA2B;YAC/E,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;;kCACjE,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;kCACrE,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;YAGzE,UAAU;gBAAC;gBAA0B;gBAA4B;aAA4B;YAC7F,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,UAAU;gBAAC;gBAAwB;gBAAyB;aAA4B;YACxF,UAAU;QACZ;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAoB;;;;;;;0CAEtD,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAMzE,8OAAC,oMAAM,CAAC,GAAG;wBACT,UAAU;wBACV,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAET,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,oMAAM,CAAC,GAAG;gCAET,UAAU;gCACV,WAAU;;kDAGV,8OAAC;wCAAI,WAAW,CAAC,mCAAmC,EAAE,QAAQ,QAAQ,CAAC,gEAAgE,CAAC;;;;;;kDAGxI,8OAAC;wCAAI,WAAW,CAAC,0CAA0C,EAAE,QAAQ,QAAQ,CAAC,oHAAoH,CAAC;kDAChM,QAAQ,IAAI;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAItB,8OAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC;wDAAe,WAAU;;0EACxB,8OAAC;gEAAI,WAAU;;;;;;4DACd;;uDAFM;;;;;;;;;;;;;;;;kDASf,8OAAC;wCAAI,WAAU;;;;;;;+BAjCV,QAAQ,EAAE;;;;;;;;;;kCAuCrB,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;8CAGpD,8OAAC;oCAAO,WAAU;8CAAuH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrJ", "debugId": null}}]}